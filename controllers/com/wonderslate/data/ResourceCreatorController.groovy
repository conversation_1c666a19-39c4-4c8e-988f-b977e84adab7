package com.wonderslate.data

import com.wonderslate.cache.DataProviderService
import com.wonderslate.institute.BatchResourcesDtl
import com.wonderslate.publish.BooksTagDtl
import com.wonderslate.publish.ExamDtl
import com.wonderslate.publish.ExamMst
import com.wonderslate.usermanagement.User
import grails.converters.JSON
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional
import groovy.json.JsonSlurper
import groovy.sql.Sql
import net.minidev.json.JSONArray
import net.minidev.json.parser.JSONParser

import java.text.DateFormat
import java.text.SimpleDateFormat


class ResourceCreatorController {
    def springSecurityService
    ResourceCreatorService resourceCreatorService
    UtilService utilService
    DataProviderService dataProviderService
    MetainfoService metainfoService
    def redisService

    def index() { }

    @Secured(['ROLE_PUBLISHER']) @Transactional
    def copyChapter(){
        String status=""
        //first check if this user has access to this book
        if (session.getAttribute("userdetails") == null) {
            session["userdetails"] = User.findByUsername(springSecurityService.currentUser.username)
        }
        ChaptersMst chaptersMst = dataProviderService.getChaptersMst(new Long(params.sourceChapterId))
        BooksMst booksMst = dataProviderService.getBooksMst(chaptersMst.bookId)
        BooksMst destBooksMst = dataProviderService.getBooksMst(new Long(params.destBookId))
        if(booksMst.publisherId==null || destBooksMst.publisherId==null) status = "Both source and destination books should have the publisher selected";
        else if(booksMst.publisherId.intValue()!=destBooksMst.publisherId.intValue()) status = "Chapter can be copied to the book of same publisher"
        else if(session["userdetails"].publisherId!=null&&(booksMst.publisherId.intValue()!=session["userdetails"].publisherId.intValue())) status = "You do not have access to copy this book"
        else{
          status =  resourceCreatorService.copyChapter(params.sourceChapterId,params.destBookId,destBooksMst.siteId)

        }


      def json = ["status":status]
      render json as JSON

    }

    @Secured(['ROLE_PUBLISHER']) @Transactional
    def copyBook(){
        String status="Success"
        def bookId
        //first check if this user has access to this book
        if (session.getAttribute("userdetails") == null) {
            session["userdetails"] = User.findByUsername(springSecurityService.currentUser.username)
        }

        BooksMst booksMst = dataProviderService.getBooksMst(new Long(params.bookId))

        if(booksMst.publisherId==null ) status = "Please select the publisher before copying";
        else if(session["userdetails"].publisherId!=null&&(booksMst.publisherId.intValue()!=session["userdetails"].publisherId.intValue())) status = "You do not have access to copy this book"
        else{
            bookId =  resourceCreatorService.copyBook(params.bookId)
            if("ai".equals(params.copyType)){
                booksMst.vendor = ""+bookId
                booksMst.save(failOnError: true, flush: true)
            }
        }

        def json = ["status":status ,"bookId":bookId]
        render json as JSON

    }

    @Secured(['ROLE_PUBLISHER']) @Transactional
    def copyQuiz() {
        def siteId = utilService.getSiteId(request,session)
        ResourceDtl resourceDtl = dataProviderService.getResourceDtl(new Long(params.resId))
        //later on we should add the publisher id check also.
        if((""+resourceDtl.siteId).equals(""+siteId)||(resourceDtl.siteId==null&&(siteId.intValue()==1))) {
            resourceCreatorService.copyQuiz(params.resId, params.chapterId, params.bookId, params.reAttempt,utilService.getSiteId(request, session))
            if("sage".equals(session["entryController"]))  {
                redirect(controller: 'wonderpublish', action: 'bookCreate', params: ['bookId': params.bookId, 'chapterId': params.chapterId])
            }
            else{
                redirect(controller: 'wonderpublish', action: 'bookCreateNew', params: ['bookId': params.bookId, 'chapterId': params.chapterId])
            }
        }
        else{
            if("sage".equals(session["entryController"]))  {
                redirect(controller: 'wonderpublish', action: 'bookCreate', params: ['bookId': params.bookId, 'chapterId': params.chapterId,'quizCopyMode':'fail'])
            }
            else{
                redirect(controller: 'wonderpublish', action: 'bookCreateNew', params: ['bookId': params.bookId, 'chapterId': params.chapterId,'quizCopyMode':'fail'])
            }
            redirect(controller: 'wonderpublish', action: 'bookCreate', params: ['bookId': params.bookId, 'chapterId': params.chapterId])
        }

    }

    @Secured(['ROLE_USER']) @Transactional
    def addQuiz() {
        HashMap returnValues =  resourceCreatorService.addQuiz(params,request,session)
        Integer siteId = utilService.getSiteId(request,session)
        if(returnValues.get("addedQuiz")) {

            if ("true".equals(params.finished)) {
                if (params.chapterId == null || ''.equals(params.chapterId)) {
                    if ("true".equals(params.bulkcreation))
                        redirect(controller: 'wonderpublish', action: 'quizcreator', params: [id: returnValues.get("resourceDtlId"), page: params.page, resourceType: "Multiple Choice Questions", useType: "quiz", mode: "edit"])
                    else {
                        User user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
                        if(user.authorities.any {
                            it.authority == "ROLE_WS_CONTENT_CREATOR"||it.authority == "ROLE_BOOK_CREATOR"
                        }&&siteId.intValue()==21) redirect(controller: 'wonderpublish', action: 'independentContent')
                        else
                            redirect([uri: '/publishTests'])
                    }
                }
                else {
                    session.removeAttribute("htmlId");
                    if ("true".equals(params.bulkcreation))
                        redirect(controller: 'wonderpublish', action: 'quizcreator', params: [id: returnValues.get("resourceDtlId"), page: params.page, chapterId: params.chapterId, bookId: params.bookId, ChaptersMst: returnValues.get("chaptersMst"), resourceType: "Multiple Choice Questions", useType: "quiz", mode: "edit"])
                    else {
                        if("sage".equals(session["entryController"]))  {
                            redirect(controller: 'wonderpublish', action: 'bookCreate', params: [chapterId: params.chapterId, bookId: params.bookId, ChaptersMst: returnValues.get("chaptersMst")])
                        }
                        else{
                            redirect(controller: 'wonderpublish', action: 'bookCreateNew', params: [chapterId: params.chapterId, bookId: params.bookId, ChaptersMst: returnValues.get("chaptersMst")])
                        }

                    }
                }
            } else {
                render "resourceDtlId=" + returnValues.get("resourceDtlId") + "&quizId=" + returnValues.get("quizId") + "&objectiveMstId=" + returnValues.get("objectiveMstId")
                return
            }
        }else{
            render "failed";
            return
        }
    }

    // reading materials --uploaded content
    @Secured(['ROLE_USER'])
    def addFile(){
        def toc=false
        if("toc".equals(params.quizMode)) toc =true;
       def chapterId = resourceCreatorService.addFile(params,flash,utilService.getSiteId(request,session))

        if("book".equals(params.from)){
            redirect(controller: 'wonderpublish', action: 'book', params: ['bookId': params.bookId, 'chapterId': chapterId,'notesCreationMode':true])
        } else if("pdfBookCreationPage".equals(params.from)){
            def json = ['bookId': params.bookId, 'chapterId': chapterId]
            render json as JSON
        }else {
            if("sage".equals(session["entryController"]))  {
                redirect(controller: 'wonderpublish', action: 'bookCreate', params: ['bookId': params.bookId, 'chapterId': chapterId, 'toc': toc])
            }
            else{
                redirect(controller: 'wonderpublish', action: 'bookCreateNew', params: ['bookId': params.bookId, 'chapterId': chapterId, 'toc': toc])
            }
        }
    }

    //Editing the zoom level
    def editPdfZoomLevel(){
        def json = resourceCreatorService.editPdfZoomLevel(params)
        render json as JSON

    }


    //reading materials - created on WS

    @Secured(['ROLE_USER']) @Transactional
    def addHTML() {
        def resId =resourceCreatorService.addHTML(params,session,utilService.getSiteId(request,session))
        ResourceDtl resourceDtl = dataProviderService.getResourceDtl(resId)
        if ("notes".equals(params.page)){
            if ("createdbyuser".equals(resourceDtl.sharing)) {
                if (params.batchIds != null && params.batchIds.length() > 0) {
                    String[] batchIds = params.batchIds.split(",")
                    for (int i = 0; i < batchIds.length; i++) {
                        BatchResourcesDtl batchResourcesDtl = new BatchResourcesDtl(batchId: new Long(batchIds[i]),
                                resId: resId, chapterId: new Integer(params.chapterId))
                        batchResourcesDtl.save(flush: true, failOnError: true)
                    }
                }
                Integer siteId = utilService.getSiteId(request,session)
                SiteMst sm = SiteMst.findById(new Long(siteId))
                if(siteId.intValue()==1 || siteId.intValue()==46 || siteId.intValue()==11 || siteId.intValue()==27 || "privatelabel".equals(session['entryController'])) redirect(controller: 'resources', action: 'ebook', params: [chapterId: params.chapterId, bookId: params.bookId,fromNotes:true,siteName:sm.siteName])
                else redirect(controller: 'wonderpublish', action: 'book', params: [chapterId: params.chapterId, bookId: params.bookId])
            } else {
                if("sage".equals(session["entryController"]))  {
                    redirect(controller: 'wonderpublish', action: 'bookCreate', params: [chapterId: params.chapterId, bookId: params.bookId])
                }
                else{
                    redirect(controller: 'wonderpublish', action: 'bookCreateNew', params: [chapterId: params.chapterId, bookId: params.bookId])
                }
            }

        }
        else {
            if ("syllabus".equals(params.page)) {
                redirect(controller: 'wonderpublish', action: 'manageTabs')
            }else{
                Integer siteId = utilService.getSiteId(request,session)
                User user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
                if(user.authorities.any {
                    it.authority == "ROLE_WS_CONTENT_CREATOR"||it.authority == "ROLE_BOOK_CREATOR"
                }&&siteId.intValue()==21) redirect(controller: 'wonderpublish', action: 'independentContent')
                else
                    redirect([uri: '/notes'])

            }

        }
    }


    //links
    @Secured(['ROLE_USER']) @Transactional
    def addlink(){
        User user = User.findByUsername(springSecurityService.currentUser.username)
        if(user.authorities.any {
            it.authority == "ROLE_WS_CONTENT_CREATOR"||it.authority == "ROLE_BOOK_CREATOR"
        }&&"app".equals(params.from)&&"Reference Videos".equals(params.resourceType)){
            RelatedVideosNew relatedVideosNew = new RelatedVideosNew(videoId: params.link, videoTitle: params.resourceName,
                    chapterId: new Integer(params.chapterId), username:springSecurityService.currentUser.username)
            relatedVideosNew.save(failOnError: true, flush: true)
            def json =  ['resId':new Integer(-1)]
            render json as JSON
        }else {
            def resId = resourceCreatorService.addlink(params, request, session)
            if ("app".equals(params.from)) {
                def json = ['resId': resId]
                render json as JSON

            } else {
                if ("sage".equals(session["entryController"])) {
                    redirect(controller: 'wonderpublish', action: 'bookCreate', params: ['bookId': params.bookId, 'chapterId': params.chapterId, 'toc': false])
                } else {
                    redirect(controller: 'wonderpublish', action: 'bookCreateNew', params: ['bookId': params.bookId, 'chapterId': params.chapterId, 'toc': false])
                }
            }
        }
    }

    @Secured(['ROLE_BOOK_CREATOR']) @Transactional
    def addIndependentContentFile(){
        def resId =resourceCreatorService.addIndependentContentFile(params,session,utilService.getSiteId(request,session))
        redirect(controller: 'wonderpublish', action: 'independentContent')
    }
    
    //links
    @Secured(['ROLE_BOOK_CREATOR']) @Transactional
    def addIndepentdentContentlink() {
        def resId = resourceCreatorService.addlink(params, request, session)
                redirect(controller: 'wonderpublish', action: 'independentContent')
        }

    @Transactional
    def addNotes(){
        def resLink=null,filename=null,resourceName=null,subject = null,chapterId=null,notesName=""
        if(params.mode=='edit') {
            session.setAttribute("htmlId",new Integer(params.id))
            ResourceDtl resourceDtl = dataProviderService.getResourceDtl(new Long(params.id))
            resLink  = resourceDtl.resLink
            filename = resourceDtl.filename
            resourceName = resourceDtl.resourceName
            subject = resourceDtl.subject

        } else if(params.mode=='create') {
           session.removeAttribute("htmlId")
            if(params.chapterId!=null&&params.bookId!=null){

                chapterId = params.chapterId
                BooksTagDtl booksTagDtl = BooksTagDtl.findById(new Long(params.bookId))
                if(booksTagDtl!=null) subject = booksTagDtl.subject
                notesName = "Notes for "+params.chapterName
            }
        }

        if(redisService.("subjectsList")==null) dataProviderService.getSubjectsList()
        List subjects = new JsonSlurper().parseText(redisService.("subjectsList"));

        [resLink:resLink, filename:filename, resourceName:resourceName, subjects:subjects, selectedSubject:subject,chapterId: chapterId, notesName:notesName]
    }

    @Transactional
    def mergeQuizzes(){
        if("view".equals(params.mode)){
            List resources = ResourceDtl.findAllByChapterIdAndResType(new Integer(params.chapterId),"Multiple Choice Questions", [sort: "id"])
            [resources:resources]
        }else{
            String [] resIds = (""+params.resIds).split(',')
            ResourceDtl parentResourceDtl = ResourceDtl.findById(new Integer(resIds[0]))
            for(int i=1;i<resIds.size();i++){
                ResourceDtl resourceDtl = ResourceDtl.findById(new Integer(resIds[i]))
                ObjectiveMst.executeUpdate("update ObjectiveMst set quiz_id="+ parentResourceDtl.resLink + " where quiz_id=" + resourceDtl.resLink)
                resourceDtl.delete(flush: true)

            }
            dataProviderService.getChapterDefaultResourcesAsString(new Long(params.chapterId))
            dataProviderService.getChapterDefaultResources(new Long(params.chapterId))
            dataProviderService.getChapterResourcesForUser(new Long(params.chapterId),springSecurityService.currentUser.username)
            dataProviderService.quizDetails(parentResourceDtl)
            redirect(controller: 'wonderpublish', action: 'bookCreateNew', params: ['bookId': params.bookId, 'chapterId': params.chapterId])

        }

    }

   def videosMigrate(){
       int offset = 0
       int  bucketSize = 100

       for(int i=0;i<2000;i++) {
           println("offset="+offset)
           String sql = "select chapter_id from related_videos " +
                   " order by id limit " + offset + "," + bucketSize
           def dataSource = grailsApplication.mainContext.getBean('dataSource')
           def sql1 = new Sql(dataSource)
           def results = sql1.rows(sql)
           if(results.size()==0){
               break
           }
           else {
               results.each { relatedVideo ->
                   migrateRelatedVideos(relatedVideo.chapter_id)

               }
           offset += bucketSize
               }

       }
       render "completed"

   }

    def migrateRelatedVideos(chapterId){
        RelatedVideos relatedVideos = RelatedVideos.findByChapterId(new Integer(chapterId))
        JSONParser parser = new JSONParser();

        JSONArray relatedVideosFromDB = (JSONArray)parser.parse(new String(relatedVideos.videos, "UTF-8"));
        relatedVideosFromDB.each {video ->
          try {
              RelatedVideosNew relatedVideosNew = new RelatedVideosNew(videoId: video.id.videoId, videoTitle: video.snippet.title, chapterId: new Integer(chapterId))
              relatedVideosNew.save(failOnError: true, flush: true)
          }catch(Exception e){
              println("exception for title "+video.snippet.title)
          }
        }

        return "done"
    }

    @Secured(['ROLE_BOOK_CREATOR']) @Transactional
    def mcqSorter() {
        String sort=""
        ResourceDtl resourceDtl = dataProviderService.getResourceDtl(new Integer(params.resId))
        ObjectiveMst objectiveMst = ObjectiveMst.findByQuizIdAndQuizSort(new Integer(resourceDtl.resLink),0)
        if(objectiveMst != null) sort = "quizSort"
        else sort = "id"
        List mcqs = ObjectiveMst.findAllByQuizId(new Integer(resourceDtl.resLink), [sort: sort])
       [mcqs:mcqs, resourceDtl:resourceDtl]
    }

    @Transactional
    def updateMCQsSortId(){
        ResourceDtl resourceDtl = dataProviderService.getResourceDtl(new Integer(params.resId))
        String[] objIds = params.mcqSortIds.split(",");
        for(int i=0;i<objIds.length;i++){
            ObjectiveMst.executeUpdate("update ObjectiveMst set quizSort =" +i+ " where id=" + objIds[i])
        }
        metainfoService.getAllChaptersMetaInfo(params.bookId)
        dataProviderService.quizDetails(resourceDtl)
        def json = ['status':"success"]
        render json as JSON

    }

    @Secured(['ROLE_BOOK_CREATOR']) @Transactional
    def sectionModifier() {
        String sort=""
        ResourceDtl resourceDtl = dataProviderService.getResourceDtl(new Integer(params.resId))
        ObjectiveMst objectiveMst = ObjectiveMst.findByQuizIdAndQuizSort(new Integer(resourceDtl.resLink),0)
        if(objectiveMst != null) sort = "quizSort"
        else sort = "id"
        List mcqs = ObjectiveMst.findAllByQuizId(new Integer(resourceDtl.resLink), [sort: sort])

        List examDtl = null
        if(resourceDtl.examId!=null) {
            ExamMst  examMst = ExamMst.findById(resourceDtl.examId)

            if (examMst != null) examDtl = ExamDtl.findAllByExamId(examMst.id)
        }
        [mcqs:mcqs, resourceDtl:resourceDtl, examDtl:examDtl]
    }

    @Transactional
    def updateSections(){
        ResourceDtl resourceDtl = dataProviderService.getResourceDtl(new Integer(params.resId))
        List mcqs = ObjectiveMst.findAllByQuizId(new Integer(resourceDtl.resLink))
        for(int i=0;i<mcqs.size();i++){
            if(request.getParameter("quiz_"+mcqs[i].id)!=null) {
                ObjectiveMst.executeUpdate("update ObjectiveMst set subject ='" + request.getParameter("quiz_"+mcqs[i].id) + "' where id=" + mcqs[i].id)
            }
        }
        metainfoService.getAllChaptersMetaInfo(params.bookId)
        dataProviderService.quizDetails(resourceDtl)
        redirect(controller: 'resourceCreator', action: 'sectionModifier', params: ['bookId': params.bookId, 'chapterId': params.chapterId, 'resId':params.resId])

    }

    @Transactional
    def updateSectionSubject(){
        resourceCreatorService.updateSectionSubject(params.from,params.to,params.subject,params.quizId)
        def json = ["status":"OK",rowIndex:params.index]
        render json as JSON
    }

    @Transactional
    def aiBookMigrate(){
        BooksMst booksMst = BooksMst.findById(new Long(params.bookId))
        [booksMst:booksMst]
    }

    @Secured(['ROLE_PUBLISHER']) @Transactional
    def copyToExistingBook(){
        String status="Success"
        //first check if this user has access to this book
        if (session.getAttribute("userdetails") == null) {
            session["userdetails"] = User.findByUsername(springSecurityService.currentUser.username)
        }

        BooksMst booksMst = dataProviderService.getBooksMst(new Long(params.sourceBookId))

        if(session["userdetails"].publisherId!=null&&(booksMst.publisherId.intValue()!=session["userdetails"].publisherId.intValue())) status = "You do not have access to copy this book"
        else{
            resourceCreatorService.copyToExistingBook(params.sourceBookId,params.destBookId)

        }

        def json = ["status":status ,"bookId":params.destBookId]
        render json as JSON

    }

    @Transactional @Secured(['ROLE_USER'])
    def migrateMCQsToSingleChapters(){
        String bookId = params.bookId
        BooksMst booksMst = dataProviderService.getBooksMst(new Long(bookId))
        // get list of chapters for this book id sort by sortOrder and then id in asc order
        List chapters = ChaptersMst.findAllByBookId(new Long(bookId),[sort: "sortOrder", order: "asc"])
        //create a interger list to store chapter ids
        List chapterIds = new ArrayList()
        String chapterName=""
        chapters.each { chapter ->
            chapterIds.add(chapter.id)
            chapterName = chapter.name
            // get list of resources for this chapter
            List resources = ResourceDtl.findAllByChapterIdAndResType(chapter.id, "Multiple Choice Questions")
            if(resources.size()>1){
                chapter.name = chapterName+" - "+resources.get(0).resourceName
                chapter.save(failOnError: true, flush: true)

                for(int i=1;i<resources.size();i++){
                    ResourceDtl resource = resources.get(i)
                    //create new chapter
                    ChaptersMst chaptersMst = new ChaptersMst(bookId: new Long(bookId), name: chapterName+" - "+resource.resourceName  ,  createdBy: springSecurityService.currentUser.username)
                    chaptersMst.save(failOnError: true, flush: true)
                    chapterIds.add(chaptersMst.id)
                    //update resource
                    resource.chapterId = chaptersMst.id
                    resource.save(failOnError: true, flush: true)
                }
            }
        }
        //update the sortOrder
        for(int i=0;i<chapterIds.size();i++){
            ChaptersMst.executeUpdate("update ChaptersMst set sortOrder =" +i+ " where id=" + chapterIds.get(i))
        }
        dataProviderService.getChaptersList(new Long(bookId))
        dataProviderService.getBooksListForUser()
        dataProviderService.getWSUnpublishedMyBooks()
        def json = ["status":"success"]
        render json as JSON

    }
}
