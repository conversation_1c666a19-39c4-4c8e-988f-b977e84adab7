package com.wonderslate.data

import com.wonderslate.cache.DataProviderService
import com.wonderslate.log.GptDefaultCreateLog
import com.wonderslate.log.AutogptLog
import com.wonderslate.sqlutil.SafeSql
import grails.converters.JSON
import grails.transaction.Transactional
import groovy.json.JsonBuilder
import groovy.json.JsonSlurper
import org.apache.commons.collections4.map.LazyMap
import org.grails.web.json.JSONObject

@Transactional
class AutogptService {

    DataProviderService dataProviderService
    def springSecurityService
    def grailsApplication
    PromptService promptService



    def getChapterMetaData(params){
        println("Entered the getChapterMetaData")
        // Get ResourceDtl record using resId
        ResourceDtl resourceDtl = ResourceDtl.findById(new Long(params.resId))
        ChaptersMst chaptersMst = ChaptersMst.findById(resourceDtl.chapterId)
        if (!resourceDtl) {
            def json = [status: "ERROR", message: "Resource not found for resId: " + params.resId]
            return json
        }
        println("The resourceDtl extract path is "+resourceDtl.extractPath)
        if (!resourceDtl.extractPath || resourceDtl.extractPath.trim().isEmpty()) {
            def json = [status: "ERROR", message: "extractPath is empty for this resource"]
            return json

        }
        String folderPath = resourceDtl.extractPath.substring(0, resourceDtl.extractPath.lastIndexOf("/"))
        File metadataFile = new File(grailsApplication.config.grails.basedir.path + "/"+folderPath+"/chapterMetadata"+ chaptersMst.id + ".txt")
        if(!metadataFile.exists())  {
            // Construct full file path
            String filePath = grailsApplication.config.grails.basedir.path + "/" + resourceDtl.extractPath
            File textFile = new File(filePath)
            //get the full content of the file to a string
            String fileContent = textFile.text

            Prompts prompts = Prompts.findByPromptType("chapterMetadataExtractor")
            String response = getLLMResponse(resourceDtl, fileContent, prompts.basePrompt)
            metadataFile.write(response)

        }
        def json = [status:"OK"]
        return json
    }


    def exerciseCollector(params) {
        int noOfQuestionsPerIteration = 5
        String allResponse = ""

        try {
            // Check if resId parameter is provided
            if (!params.resId) {
                def json = [status: "ERROR", message: "resId parameter is required"]
                return json
            }

            // Get ResourceDtl record using resId
            ResourceDtl resourceDtl = ResourceDtl.findById(new Long(params.resId))
            ChaptersMst chaptersMst = ChaptersMst.findById(resourceDtl.chapterId)
            //check if there is instance of resourceDtl for given chapterId and resourceName starting with "Exercise"
            boolean exerciseCreated = false
            ResourceDtl exerciseResourceDtl = ResourceDtl.findByChapterIdAndResourceNameLike(chaptersMst.id,"Exercise%")
            if(exerciseResourceDtl!=null) {
                try {
                    ObjectiveMst objectiveMst = ObjectiveMst.findByQuizId(new Integer(exerciseResourceDtl.resLink))
                    if (objectiveMst != null) exerciseCreated = true
                }catch (Exception e){
                    exerciseCreated = false
                }
            }
            if(!exerciseCreated) {
                BooksMst booksMst = dataProviderService.getBooksMst(chaptersMst.bookId)
                if (!resourceDtl) {
                    def json = [status: "ERROR", message: "Resource not found for resId: " + params.resId]
                    return json
                }

                // Check if extractPath is not empty
                if (!resourceDtl.extractPath || resourceDtl.extractPath.trim().isEmpty()) {
                    def json = [status: "ERROR", message: "extractPath is empty for this resource"]
                    return json
                }

                // Construct full file path
                String filePath = grailsApplication.config.grails.basedir.path + "/" + resourceDtl.extractPath
                File textFile = new File(filePath)

                if (!textFile.exists()) {
                    def json = [status: "ERROR", message: "Text file not found at path: " + filePath]

                    return json
                }

                // Read file content in chunks of 20000 characters
                def chunkSize = 20000
                def chunkNumber = 1

                ResourceDtl resourceDtlInstance = null
                if(exerciseResourceDtl!=null) resourceDtlInstance = exerciseResourceDtl
                textFile.withReader('UTF-8') { reader ->
                    char[] buffer = new char[chunkSize]
                    StringBuilder currentChunk = new StringBuilder()
                    int charsRead

                    while ((charsRead = reader.read(buffer, 0, chunkSize)) != -1) {
                        currentChunk.append(buffer, 0, charsRead)

                        // If we have read exactly chunkSize characters, look for next paragraph break
                        if (charsRead == chunkSize) {
                            // Read additional characters until we find a paragraph break
                            int nextChar
                            while ((nextChar = reader.read()) != -1) {
                                currentChunk.append((char) nextChar)

                                // Check for paragraph break (double newline)
                                String content = currentChunk.toString()
                                if (content.endsWith("\n\n") || content.endsWith("\r\n\r\n")) {
                                    break
                                }
                            }
                        }


                        def response = examplesAndExercisesExtractor(resourceDtl, currentChunk.toString())

                        String question, answer
                        //sometimes response is "[]\n[]\n" we have check for that also
                        if (response != null && response.size() > 0 && !"[]\n[]\n".equals(response)) {
                            //for each response get the question element and print it
                            if (resourceDtlInstance == null) {
                                QuizIdGenerator quizIdGenerator = new QuizIdGenerator()
                                quizIdGenerator.save()

                                resourceDtlInstance = new ResourceDtl()
                                resourceDtlInstance.resLink = quizIdGenerator.id
                                resourceDtlInstance.createdBy = springSecurityService.currentUser.username
                                resourceDtlInstance.resType = "QA"
                                resourceDtlInstance.chapterId = resourceDtl.chapterId
                                resourceDtlInstance.resourceName = "Exercise Solutions"
                                resourceDtlInstance.save(failOnError: true, flush: true)


                            }
                            try {
                                int numberOfQuestions = response.size()
                                println("The number of exercise questions are "+numberOfQuestions)
                                int currentQuestionIndex = 0
                                while(currentQuestionIndex<numberOfQuestions){
                                    String inputQuestions = "The questions are \n"

                                    int noOfQuestionsForLoop = noOfQuestionsPerIteration
                                    if(numberOfQuestions-currentQuestionIndex<noOfQuestionsPerIteration){
                                        noOfQuestionsForLoop = numberOfQuestions-currentQuestionIndex
                                    }
                                    for(int i=currentQuestionIndex;i<currentQuestionIndex+noOfQuestionsForLoop;i++){
                                        inputQuestions += (i+1)+". "+response[i].question+"\n"
                                    }
                                    println("The exercise inputQuestions are "+inputQuestions)
                                    def jsonAnswer = getSolution(resourceDtl, inputQuestions, chaptersMst.id, booksMst.id,"solutionCreator")
                                    println("The jsonAnswer is "+jsonAnswer.answer)
                                    def jsonAnswerList = new JsonSlurper().parseText(jsonAnswer.answer)
                                    jsonAnswerList.each { json ->
                                        answer = json.solution
                                        //add the question and answer to ObjectiveMst
                                        ObjectiveMst om = new ObjectiveMst(quizId: new Integer(resourceDtlInstance.resLink), quizType: "QA", question:  json.question,
                                                answer: json.solution,
                                                difficultylevel: json.difficultyLevel, qType: json.questionType, answerDescription: json.explanation, bloomType: json.bloomLevel)
                                        om.save(failOnError: true, flush: true)
                                    }
                                    currentQuestionIndex += noOfQuestionsForLoop

                                }

                            } catch (Exception e) {
                                println("Exception in exerciseCollector: " + e.message)
                            }

                        }

                        chunkNumber++
                        currentChunk.setLength(0) // Clear the buffer for next chunk
                    }
                }


                dataProviderService.getChaptersList(chaptersMst.bookId);
                def json = [status: "OK", message: "File processed successfully"]
                return json
            }else{
                def json = [status: "OK", message: "Exercises already created"]
                return json
            }
        } catch (Exception e) {
            println("Exception in exerciseCollector: " + e.message)
            e.printStackTrace()
            def json = [status: "ERROR", message: "Error processing file: " + e.message]
            return json
        }
    }

    def examplesAndExercisesExtractor(ResourceDtl resourceDtl,String inputText){
        try {
            Prompts prompts = Prompts.findByPromptType("exerciseExtractor")
            String responseAnswer = getLLMResponse(resourceDtl,inputText,prompts.basePrompt)
            List responseList =null
            try {
                responseList = new JsonSlurper().parseText(fixJsonString(responseAnswer))
            }catch (Exception e){
                println("Exception in parsing responseAnswer "+e.getMessage())
            }
            return responseList
        }catch (Exception e){
            return "Exception happened: "+e.getMessage()
        }
    }

    def getLLMResponse(ResourceDtl resourceDtl,String inputText,String basePrompt){
        try {
            def file = new File("extractData" + resourceDtl.id + ".txt");
            if(file.exists()) file.delete()
            file.createNewFile()
            file.append(inputText);
            URL url = new URL(grailsApplication.config.grails.aiserver.url + "/retrieveDataAdminText")
            HttpURLConnection conn = (HttpURLConnection) url.openConnection()
            conn.setRequestMethod("POST")

            String boundary = "===" + System.currentTimeMillis() + "==="
            conn.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + boundary)
            conn.setDoOutput(true)

            String prompt = basePrompt
            try {
                OutputStream output = conn.getOutputStream();
                PrintWriter writer = new PrintWriter(new OutputStreamWriter(output, "UTF-8"), true)
                writer.append("\r\n").flush()
                writer.append("--" + boundary).append("\r\n")
                writer.append("Content-Disposition: form-data; name=\"prompt\"").append("\r\n")
                writer.append("Content-Type: text/plain; charset=UTF-8").append("\r\n")
                writer.append("\r\n").flush()
                output.write(prompt.getBytes("UTF-8"))
                output.flush()

                // Send file.
                writer.append("\r\n").flush()
                writer.append("--" + boundary).append("\r\n")
                writer.append("Content-Disposition: form-data; name=\"file\"; filename=\"file.txt\"").append("\r\n")
                writer.append("Content-Type: text/plain; charset=UTF-8").append("\r\n")
                writer.append("\r\n").flush()
                output.write(file.text.getBytes("UTF-8"))
                output.flush()

                // End of multipart/form-data.
                writer.append("\r\n").flush()
                writer.append("--" + boundary + "--").append("\r\n").flush()
            }
            catch (Exception e) {
                e.printStackTrace()
            }
            String response = conn.getInputStream().getText()
            def json = new JsonSlurper().parseText(response)
            String responseAnswer = jsonCleaner(json.response)

            //delete the file
            file.delete()
            return responseAnswer
        }catch (Exception e){
            return "Exception happened: "+e.getMessage()
        }
    }

    def getSolution(ResourceDtl resourceDtl,question,chapterId,bookId,String promptName){
        JSONObject requestBody = new JSONObject()
        Prompts prompt = Prompts.findByPromptType(promptName)
        String customPrompt = prompt.basePrompt

        requestBody.put("namespace",resourceDtl.vectorStored)
        requestBody.put("resId",resourceDtl.id)
        requestBody.put("resType","userInput")
        requestBody.put("chatHistory","")
        requestBody.put("query",question)
        requestBody.put("chapterId",chapterId)
        requestBody.put("bookId",bookId)
        requestBody.put("customPrompt", customPrompt)
        println("The request body is "+requestBody.toString())
          URL url = new URL(grailsApplication.config.grails.aiserver.url+"/retrieveData")

        HttpURLConnection connection = (HttpURLConnection) url.openConnection()
        connection.setRequestMethod("POST")
        connection.setRequestProperty("Content-Type", "application/json")
        connection.setDoOutput(true)
        connection.setDoInput(true)
        connection.connect()
        def writer = new BufferedWriter(new OutputStreamWriter(connection.getOutputStream()))
        writer.write(requestBody.toString())
        writer.flush()
        writer.close()
        def responseCode = connection.getResponseCode()
        if(responseCode==200){
            def reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))
            def response = reader.readLine()
            def jsonSlurper = new JsonSlurper()
            def jsonResponse = jsonSlurper.parseText(response)
            return jsonResponse
        }else{  return null}

    }

    def removeExtraCommas(String json) {
        // This regex will find one or more commas (,) followed by zero or more whitespace characters (\s*)
        // and then another comma (,), and replace them with a single comma.
        // It's designed to specifically target consecutive commas.
        // The first pattern `[,]+\s*,` handles cases like `,,` or `,,,`
        // The second pattern `,\s*]` handles cases where a comma might be followed by a closing bracket (e.g., `},,]`)
        // The third pattern `,\s*` would catch any stray comma followed by whitespace, but the above two are more specific to the issue.

        // Let's refine the regex for better handling of your specific problem:
        // We want to match one or more commas that are not immediately followed by a '{' or a ']' or end of string.
        // And replace multiple commas with a single comma, while also cleaning up commas before ']'
        String cleaned = json.replaceAll(",\\s*,+", ","); // Replace multiple commas with a single comma
        cleaned = cleaned.replaceAll(",\\s*]", "]");      // Remove commas just before a closing bracket

        return cleaned;
    }

    def jsonCleaner(String jsonInput){
        jsonInput = jsonInput.replaceAll("`", "")
        jsonInput = jsonInput.replaceAll("json", "")
        jsonInput = jsonInput.replaceAll("\n", "")
        jsonInput = jsonInput.replaceAll("\r", "")
        //next i want to replace ][ with comma
        jsonInput = jsonInput.replaceAll("\\]\\[", ",")
        // replace },] with }]
        jsonInput = jsonInput.replaceAll("},]", "}]")

        //replace  .^ with blank
        jsonInput = jsonInput.replaceAll("\\.\\^", "")
        jsonInput = removeExtraCommas(jsonInput)
        jsonInput = fixJsonString(jsonInput)
        return jsonInput
    }

    def questionBankBuilder(params){
        ResourceDtl resourceDtl = ResourceDtl.findById(new Long(params.resId))
        ChaptersMst chaptersMst = ChaptersMst.findById(resourceDtl.chapterId)
        //find if there is a resourceDtl instance for the given chapterId and resourceName starting with "QuestionBank"
        ResourceDtl questionBankResourceDtl = ResourceDtl.findByChapterIdAndResourceNameLike(chaptersMst.id,"QuestionBank%")
        int noOfQuestionsPerIteration = 5
        if(questionBankResourceDtl==null) {

            List responseList = null
            ResourceDtl qaResourceDtl, mcqResourceDtl
            List<String> questionList = []
            try {
                //get the metadata from file
                String folderPath = resourceDtl.extractPath.substring(0, resourceDtl.extractPath.lastIndexOf("/"))
                File metadataFile = new File(grailsApplication.config.grails.basedir.path + "/" + folderPath + "/chapterMetadata" + chaptersMst.id + ".txt")
                def json = new JsonSlurper().parseText(metadataFile.text)

// Now access the subtopics list
                def subtopics = json.metadata.subtopics

                Prompts prompts = Prompts.findByPromptType("questionBankBuilder")
                // Construct full file path, get the path till the file name in the resourceDtl.extractPath
                String filePath = grailsApplication.config.grails.basedir.path + "/" + resourceDtl.extractPath

                File textFile = new File(filePath)
                String fileContent = textFile.text
                String question, answer
                HashMap questionMap = new HashMap()
                String subTopicName

                subtopics.each { subtopic ->

                    String promptText = prompts.basePrompt
                    promptText = promptText.replaceAll("SUBTOPICMETADATA", "" + subtopic)


                    String responseAnswer = getLLMResponse(resourceDtl, fileContent, promptText)
                    try {

                        responseList = new JsonSlurper().parseText(fixJsonString(responseAnswer))
                        int numberOfQuestions = responseList.size()
                        println("The number of subtopic questions are "+numberOfQuestions)
                        int currentQuestionIndex = 0
                        while(currentQuestionIndex<numberOfQuestions) {
                            String inputQuestions = "The subtopic questions are \n"

                            int noOfQuestionsForLoop = noOfQuestionsPerIteration
                            if (numberOfQuestions - currentQuestionIndex < noOfQuestionsPerIteration) {
                                noOfQuestionsForLoop = numberOfQuestions - currentQuestionIndex
                            }
                            for (int i = currentQuestionIndex; i < currentQuestionIndex + noOfQuestionsForLoop; i++) {
                                inputQuestions += (i + 1) + ". " + responseList[i].question + "\n"
                                questionList.add(responseList[i].question)
                            }
                            println("The question inputQuestions are " + inputQuestions)
                            def jsonAnswer = getSolution(resourceDtl, inputQuestions, chaptersMst.id, chaptersMst.bookId, "additionQuestionsCreator")
                            println("The jsonAnswer is " + jsonAnswer)
                            if(jsonAnswer!=null) {
                                def newQuestionsList = jsonCleaner(jsonAnswer.answer)
                                def json1 = new JsonSlurper().parseText(newQuestionsList)
                                json1.each { question1 ->
                                    questionList.add(question1.question)
                                }
                            }
                            currentQuestionIndex += noOfQuestionsForLoop
                        }


                    } catch (Exception e) {
                        println("Exception in parsing responseAnswer1 " + e.getMessage())

                    }
                }

                println("Total questions are "+questionList.size())
                try{
                    int currentQuestionIndex = 0
                    int numberOfQuestions = questionList.size()
                    while(currentQuestionIndex<questionList.size()) {
                        String inputQuestions = "The questions are \n"

                        int noOfQuestionsForLoop = noOfQuestionsPerIteration
                        if (numberOfQuestions - currentQuestionIndex < noOfQuestionsPerIteration) {
                            noOfQuestionsForLoop = numberOfQuestions - currentQuestionIndex
                        }
                        for (int i = currentQuestionIndex; i < currentQuestionIndex + noOfQuestionsForLoop; i++) {
                            inputQuestions += (i + 1) + ". " + questionList[i] + "\n"
                        }
                        def jsonAnswer = getSolution(resourceDtl, inputQuestions, chaptersMst.id, chaptersMst.bookId, "solutionCreator")
                        println("The jsonAnswer is " + jsonAnswer.answer)
                        def jsonAnswerList = new JsonSlurper().parseText(fixJsonString(jsonAnswer.answer))

                        jsonAnswerList.each { json1 ->
                            json = json1
                            answer = json.solution
                            if (json.questionType == "MCQ") {
                                if (mcqResourceDtl == null) {
                                    QuizIdGenerator quizIdGenerator = new QuizIdGenerator()
                                    quizIdGenerator.save()

                                    mcqResourceDtl = new ResourceDtl()
                                    mcqResourceDtl.resLink = quizIdGenerator.id
                                    mcqResourceDtl.createdBy = springSecurityService.currentUser.username
                                    mcqResourceDtl.resType = "Multiple Choice Questions"
                                    mcqResourceDtl.chapterId = resourceDtl.chapterId
                                    mcqResourceDtl.resourceName = "QuestionBank MCQs"
                                    mcqResourceDtl.gptResourceType = "mcq"
                                    mcqResourceDtl.save(failOnError: true, flush: true)

                                    GptDefaultCreateLog gptDefaultCreateLog = GptDefaultCreateLog.findByReadingMaterialResIdAndPromptType(resourceDtl.id, "mcq")
                                    if (gptDefaultCreateLog == null) {
                                        gptDefaultCreateLog = new GptDefaultCreateLog(resId: mcqResourceDtl.id, promptType: "mcq", prompt: "Create MCQs (Multiple Choice Questions)", response: "MCQ",
                                                readingMaterialResId: resourceDtl.id, username: springSecurityService.currentUser.username, promptLabel: "Create MCQs (Multiple Choice Questions)")
                                        gptDefaultCreateLog.save(failOnError: true, flush: true)
                                    } else {
                                        gptDefaultCreateLog.resId = mcqResourceDtl.id
                                        gptDefaultCreateLog.save(failOnError: true, flush: true)
                                    }
                                }
                                ObjectiveMst om = new ObjectiveMst(quizId: new Integer(mcqResourceDtl.resLink), quizType: "MCQ", question: json.questionText,
                                        answer: json.solution,
                                        difficultylevel: json.difficultyLevel, qType: json.questionType, answerDescription: json.explanation,
                                        bloomType: json.bloomLevel, subTopic: subtopics.heading,
                                        option1: json.option1,
                                        option2: json.option2,
                                        option3: json.option3,
                                        option4: json.option4,
                                        answer1: json.correctAnswer.equals("option1") ? "Yes" : null,
                                        answer2: json.correctAnswer.equals("option2") ? "Yes" : null,
                                        answer3: json.correctAnswer.equals("option3") ? "Yes" : null,
                                        answer4: json.correctAnswer.equals("option4") ? "Yes" : null)
                                om.save(failOnError: true, flush: true)

                            } else {
                                if (qaResourceDtl == null) {
                                    QuizIdGenerator quizIdGenerator = new QuizIdGenerator()
                                    quizIdGenerator.save()

                                    qaResourceDtl = new ResourceDtl()
                                    qaResourceDtl.resLink = quizIdGenerator.id
                                    qaResourceDtl.createdBy = springSecurityService.currentUser.username
                                    qaResourceDtl.resType = "QA"
                                    qaResourceDtl.chapterId = resourceDtl.chapterId
                                    qaResourceDtl.resourceName = "QuestionBank QnA"
                                    qaResourceDtl.gptResourceType = "qna"
                                    qaResourceDtl.save(failOnError: true, flush: true)
                                    GptDefaultCreateLog gptDefaultCreateLog = GptDefaultCreateLog.findByReadingMaterialResIdAndPromptType(resourceDtl.id, "qna")
                                    if (gptDefaultCreateLog == null) {
                                        gptDefaultCreateLog = new GptDefaultCreateLog(resId: qaResourceDtl.id, promptType: "qna", prompt: "Create Question & Answers", response: "QA",
                                                readingMaterialResId: resourceDtl.id, username: springSecurityService.currentUser.username, promptLabel: "Create Question & Answers")
                                        gptDefaultCreateLog.save(failOnError: true, flush: true)
                                    } else {
                                        gptDefaultCreateLog.resId = qaResourceDtl.id
                                        gptDefaultCreateLog.save(failOnError: true, flush: true)
                                    }
                                }
                                ObjectiveMst om = new ObjectiveMst(quizId: new Integer(qaResourceDtl.resLink), quizType: "QA", question: json.question,
                                        answer: json.solution,
                                        difficultylevel: json.difficultyLevel, qType: json.questionType, answerDescription: json.explanation,
                                        bloomType: json.bloomLevel, subTopic: subtopics.heading)
                                om.save(failOnError: true, flush: true)
                            }
                        }
                        currentQuestionIndex += noOfQuestionsForLoop
                    }
                }catch (Exception e){
                    println("Exception in parsing responseAnswer5 " + e.getMessage())
                }


            } catch (Exception e) {
                println("Exception in parsing responseAnswer2 " + e.toString())
            }
            def json = [status: "OK", totalQuestions: questionList.size()]
            return json
        }else {
            def json = [status: "OK", message: "Question bank already created", totalQuestions: "Not Counted"]
            return json
        }
    }

    String fixJsonString(String inputJson) {
        // Replace LaTeX-specific sequences with properly escaped versions
     /**   String fixedJson = inputJson
                .replace("\\\\\\\\", "FOURBACKSLASH")
                .replace("\\\\", "TWOBACKSLASH")
                .replace("\\", "ONEBACKSLASH")
        fixedJson = fixedJson.replace("FOURBACKSLASH", "\\\\\\\\\\\\\\\\")
                .replace("TWOBACKSLASH", "\\\\\\\\")
                .replace("ONEBACKSLASH", "\\\\")*/


        //replace double quotes "" with nothing



        return inputJson;

    }

    def storePdfVectors(params){
        def chapterId = params.chapterId
        if(params.resId==null){
            List readingMaterials = ResourceDtl.findAllByChapterIdAndResTypeAndSharingIsNullAndGptResourceTypeIsNull(new Integer(params.chapterId),"Notes", [sort: "id", order: "asc"])
            if(readingMaterials.size()>0)
                params.put("resId",""+readingMaterials[0].id)
            else {
                def json = [status: "Error", message: "No PDF found for this chapter"]
                return json
            }
        }
        def resId = params.resId
        ResourceDtl documentInstance = dataProviderService.getResourceDtl(new Long(resId))
        println("*** have come this far in store vectors")
        if (documentInstance == null) {
            def json = [status: "Error", message: "Document not found."]
            return json

        } else {
            try {
                String namespace
                int resCode
                if(documentInstance.vectorStored==null) {
                    String index = promptService.getIndex("users")
                    namespace = index + "_" + chapterId + "_" + resId
                    String filePath = documentInstance.resLink
                    resCode = newUserPDFCreation( filePath, namespace)
                    documentInstance.vectorStored = namespace
                    documentInstance.save(failOnError: true, flush: true)
                }else{
                    namespace = documentInstance.vectorStored
                    resCode = 200
                }
                def res = ["status":"OK","message":"PDF Vectors stored successfully","resCode":resCode,namespace: namespace,resId:documentInstance.id]
                return res
            }catch(Exception e){
                def err = ["status":"Error","message":e.message]
                return err
            }

        }
    }

    def newUserPDFCreation(filePath,namespace){
        URL url = new URL(grailsApplication.config.grails.aiserver.url+"/processPDFVectorNew")
        HttpURLConnection connection = (HttpURLConnection) url.openConnection()
        connection.setRequestMethod("POST")
        connection.setRequestProperty("Content-Type", "application/json")
        connection.setDoOutput(true)
        connection.setDoInput(true)
        connection.connect()
        def writer = new BufferedWriter(new OutputStreamWriter(connection.getOutputStream()))

        def json = new JsonBuilder([namespace: namespace, filePath: filePath])
        writer.write(json.toString())
        writer.flush()
        writer.close()
        def responseCode = connection.getResponseCode()
        return responseCode
    }

    def runAutoGPT(String chapterId){
        def params = new HashMap()
        params.put("chapterId",chapterId)
        ResourceDtl resourceDtl
        List readingMaterials = ResourceDtl.findAllByChapterIdAndResTypeAndSharingIsNullAndGptResourceTypeIsNull(new Integer(params.chapterId),"Notes", [sort: "id", order: "asc"])
        if(readingMaterials.size()>0) {
            params.put("resId", "" + readingMaterials[0].id)
            resourceDtl = readingMaterials[0]
        }
        else {
            def json = [status: "Error", message: "No PDF found for this chapter"]
            return json
        }
        println("params: "+params)
        println("calling storePdfVectors")
       def json1 = storePdfVectors(params)
        println("json1: "+json1)
        println("calling getChapterMetaData")

        getChapterMetaData(params)
        println("calling exerciseCollector")
        exerciseCollector(params)
        println("calling questionBankBuilder")
        questionBankBuilder(params)
        println("calling deleteEmbeddings")
        deleteEmbeddings(resourceDtl)

        //check if this chapter is in autogptLog and if it is then update the status to completed
        AutogptLog autogptLog = AutogptLog.findByChapterId(new Long(chapterId))
        if(autogptLog!=null) {
            autogptLog.gptStatus = "completed"
            autogptLog.dateCompleted = new Date()
            autogptLog.save(failOnError: true, flush: true)
        }
        def json = [status:"OK",message:"AutoGPT task completed"]
        return json
    }

    def autoGPTRunner()
    {
        int noOfParallelTasks = 3
        KeyValueMst keyValueMst = KeyValueMst.findByKeyName("numberOfParallelAutoGPTTasks")
        if(keyValueMst!=null) {
            noOfParallelTasks = Integer.parseInt(keyValueMst.keyValue)
        }
        def autogptLogs = AutogptLog.findAllByGptStatus("running")
        println("Number of running tasks are modified "+autogptLogs.size())
        if(autogptLogs.size()<noOfParallelTasks) {
            println("Entering the if condition")
            def autogptLog = AutogptLog.findAllByGptStatusIsNull([sort: "id", order: "asc"])
            if (autogptLog.size() > 0) {
                println("Starting for chapterID " + autogptLog[0].chapterId)
                autogptLog[0].gptStatus = "running"
                autogptLog[0].dateStarted = new Date()
                autogptLog[0].save(failOnError: true, flush: true)
                println("Running AutoGPT task for chapter " + autogptLog[0].chapterId)
                runAutoGPT(""+autogptLog[0].chapterId)
            }
        }

    }

    def deleteEmbeddings(ResourceDtl resourceDtl) {
        if (resourceDtl.vectorStored != null) {
            JSONObject requestBody = new JSONObject()

            requestBody.put("namespace", resourceDtl.vectorStored)
            println("The request body is " + requestBody.toString())
            URL url = new URL(grailsApplication.config.grails.aiserver.url + "/delete-namespace")

            HttpURLConnection connection = (HttpURLConnection) url.openConnection()
            connection.setRequestMethod("POST")
            connection.setRequestProperty("Content-Type", "application/json")
            connection.setDoOutput(true)
            connection.setDoInput(true)
            connection.connect()
            def writer = new BufferedWriter(new OutputStreamWriter(connection.getOutputStream()))
            writer.write(requestBody.toString())
            writer.flush()
            writer.close()
            def responseCode = connection.getResponseCode()
            if (responseCode == 200) {
                println("Embeddings deleted successfully")
                resourceDtl.vectorStored = null
                resourceDtl.save(failOnError: true, flush: true)
                println("removed vectorStored from resourceDtl")
                def reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))
                def response = reader.readLine()
                def jsonSlurper = new JsonSlurper()
                def jsonResponse = jsonSlurper.parseText(response)
                return jsonResponse
            } else {
                return null
            }

        }
    }

    def checkPendingJobs(){
        println("Checking for pending jobs")
        String sql  = " SELECT id " +
                " FROM wslog.autogpt_log\n" +
                " WHERE TIMESTAMPDIFF(HOUR, date_started, SYSDATE()) > 2\n" +
                " and date_completed is null"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wslog')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)
        println("Number of pending jobs are "+results.size())
        results.each { log ->
            println("Aborting job "+log.id)
            AutogptLog autogptLog = AutogptLog.findById(new Long(log.id))
            autogptLog.gptStatus = null
            autogptLog.timesAborted = autogptLog.timesAborted!=null?autogptLog.timesAborted+1:1
            autogptLog.save(failOnError: true, flush: true)
        }
    }

    def getChapterResources(String bookId){
        List chapters = ChaptersMst.findAllByBookId(new Integer(bookId))
        List chapterDetails = []
        chapters.each { chapter ->
            int noOfExercises = 0
            int noOfQB = 0
            int noOfQBMCQs = 0

            ResourceDtl resourceDtl = ResourceDtl.findByChapterIdAndResTypeAndResourceName(new Integer(""+chapter.id),"QA","Exercise Solutions")
            if(resourceDtl!=null) {
                List exercises = ObjectiveMst.findAllByQuizId(new Integer(resourceDtl.resLink))
                noOfExercises = exercises.size()
            }

            List resources = ResourceDtl.findAllByChapterIdAndSharingIsNullAndGptResourceTypeIsNull(new Integer(""+chapter.id))
            chapterDetails << [chapterId: chapter.id, chapterName: chapter.name, resources: resources]
        }
        return chapterDetails
    }
}
